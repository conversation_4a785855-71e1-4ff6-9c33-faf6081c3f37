import React, { useState } from 'react';
import { Button, Space } from 'antd';
import { WordViewer } from './index';

const TestDocxPreview: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [viewerMode, setViewerMode] = useState<'office' | 'mammoth' | 'both'>('both');
  
  // Sample docx file URL - replace with your actual file URL
  const sampleFileUrl = 'https://example.com/sample.docx';
  
  return (
    <div style={{ padding: '20px' }}>
      <h2>Test WordViewer with docx-preview</h2>
      
      <Space direction="vertical" size="middle">
        <div>
          <h3>Viewer Mode:</h3>
          <Space>
            <Button 
              type={viewerMode === 'office' ? 'primary' : 'default'}
              onClick={() => setViewerMode('office')}
            >
              Office Only
            </Button>
            <Button 
              type={viewerMode === 'mammoth' ? 'primary' : 'default'}
              onClick={() => setViewerMode('mammoth')}
            >
              DocxPreview Only
            </Button>
            <Button 
              type={viewerMode === 'both' ? 'primary' : 'default'}
              onClick={() => setViewerMode('both')}
            >
              Both
            </Button>
          </Space>
        </div>
        
        <Button 
          type="primary" 
          onClick={() => setIsOpen(true)}
        >
          Open Word Viewer
        </Button>
      </Space>
      
      <WordViewer
        fileUrl={sampleFileUrl}
        fileName="Sample Document"
        open={isOpen}
        onClose={() => setIsOpen(false)}
        viewerMode={viewerMode}
        width="90vw"
        height="80vh"
      />
    </div>
  );
};

export default TestDocxPreview;
