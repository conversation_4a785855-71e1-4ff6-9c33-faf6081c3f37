# WordViewer Component - Đ<PERSON> Sửa Lỗi

## 🔧 **Các lỗi đã được sửa:**

### **1. Lỗi Logic Nghiêm Trọng**
- **Vấn đề**: Code sử dụng sai API của `docx-preview`
- **Lỗi cũ**: `const result = await docx.renderAsync(...); setHtmlContent(result.value);`
- **Sửa**: `docx-preview` render trực tiếp vào DOM, không trả về HTML string

### **2. Lỗi Type Definition**
- **Vấn đề**: <PERSON><PERSON> khoảng trắng thừa trong type definition
- **Lỗi cũ**: `"office" | " docx-preview" | "both"`
- **Sửa**: `"office" | "docx-preview" | "both"`

### **3. Lỗi Naming Inconsistency**
- **Vấn đề**: Vẫn sử dụng tên `MammothViewer` nhưng thực tế dùng `docx-preview`
- **Sửa**: Đ<PERSON><PERSON> tên thành `DocxPreviewViewer` và thêm backward compatibility

### **4. Lỗi Performance**
- **Vấn đề**: Components được tạo lại mỗi render
- **Sửa**: Sử dụng `useCallback` và `useMemo` để optimize

## 🚀 **Cách sử dụng:**

### **Basic Usage:**
```typescript
import { WordViewer } from '@/components/WordViewer';

<WordViewer
  fileUrl="https://example.com/document.docx"
  fileName="My Document"
  open={isOpen}
  onClose={() => setIsOpen(false)}
  viewerMode="both" // "office" | "docx-preview" | "both"
/>
```

### **Presets có sẵn:**
```typescript
import { 
  WordViewerOfficeOnly,
  WordViewerDocxPreviewOnly, 
  WordViewerMammothOnly, // deprecated, sử dụng DocxPreviewOnly
  WordViewerFullscreen,
  WordViewerMobile 
} from '@/components/WordViewer';
```

### **Hook Usage:**
```typescript
import { useWordViewer } from '@/components/WordViewer';

const { 
  isOpen, 
  openWordViewer, 
  closeWordViewer,
  currentFileUrl,
  currentFileName 
} = useWordViewer("both");

// Mở viewer
openWordViewer("https://example.com/doc.docx", "Document Name", "docx-preview");
```

## 🧪 **Testing:**

Sử dụng component test:
```typescript
import WordViewerTest from '@/components/WordViewer/WordViewerTest';

// Render component này để test các chế độ khác nhau
<WordViewerTest />
```

## 📋 **Viewer Modes:**

1. **"office"**: Chỉ sử dụng Microsoft Office Online Viewer
   - ✅ Hiển thị chính xác 100%
   - ❌ Cần internet, có thể bị chặn CORS

2. **"docx-preview"**: Chỉ sử dụng docx-preview library
   - ✅ Hoạt động offline
   - ✅ Không bị CORS
   - ❌ Có thể không hiển thị 100% chính xác với documents phức tạp

3. **"both"**: Hiển thị cả hai options trong tabs
   - ✅ Người dùng có thể chọn chế độ phù hợp
   - ✅ Fallback khi một chế độ không hoạt động

## 🔍 **Troubleshooting:**

### **Nếu Word vẫn không hiển thị:**

1. **Kiểm tra URL file:**
   - File phải accessible từ browser
   - Không bị CORS policy chặn
   - File phải là định dạng .docx hoặc .doc

2. **Kiểm tra Console:**
   ```javascript
   // Mở Developer Tools > Console để xem lỗi
   ```

3. **Test với file mẫu:**
   ```typescript
   // Sử dụng WordViewerTest component để test
   ```

## 📦 **Dependencies:**

Đảm bảo đã cài đặt:
```json
{
  "docx-preview": "^0.3.6",
  "mammoth": "^1.11.0" // optional, có thể remove nếu không dùng
}
```

## 🔄 **Migration từ version cũ:**

Không cần thay đổi code, tất cả API giữ nguyên. Component tự động sử dụng `docx-preview` thay vì `mammoth.js`.
