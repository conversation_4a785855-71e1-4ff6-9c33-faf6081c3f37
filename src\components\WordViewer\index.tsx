import React, {useState, useEffect, useMemo, useCallback, useRef} from "react";
import {<PERSON>dal, Spin, Alert, Tabs, Button, Space} from "antd";
import {FileWordOutlined, ReloadOutlined, DownloadOutlined} from "@ant-design/icons";
import {isEqual} from "lodash";
import {memo} from "react";
import "./index.default.scss";
import {IWordViewerProps} from "./types";

const WordViewerComponent: React.FC<IWordViewerProps> = ({
  fileUrl,
  fileName = "Word Document",
  open,
  onClose,
  width = "95vw",
  height = "85vh",
  destroyOnClose = true,
  containerStyle = {},
  viewerMode = "both",
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [loading, setLoading] = useState(false);
  const [htmlContent, setHtmlContent] = useState<string>("");
  const [error, setError] = useState<string>("");
  const [activeTab, setActiveTab] = useState<string>("office");

  // Microsoft Office Online Viewer URL
  const officeViewerUrl = useMemo(() => {
    if (!fileUrl) return "";
    return `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(fileUrl)}`;
  }, [fileUrl]);

  // Load Word document using docx-preview
  const loadWordDocument = useCallback(async () => {
    if (!fileUrl || viewerMode === "office") return;

    setLoading(true);
    setError("");

    try {
      // Dynamic import docx-preview
      const docx = await import("docx-preview");

      // Fetch the Word document
      const response = await fetch(fileUrl);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const arrayBuffer = await response.arrayBuffer();

      // Clear container trước khi render
      if (containerRef.current) {
        containerRef.current.innerHTML = "";
      }

      // Render document trực tiếp vào container
      await docx.renderAsync(arrayBuffer, containerRef.current, null, {
        className: "docx-preview",
        inWrapper: true,
        ignoreWidth: false,
        ignoreHeight: false,
        ignoreFonts: false,
        breakPages: true,
        ignoreLastRenderedPageBreak: true,
        experimental: false,
        trimXmlDeclaration: true,
        useBase64URL: false,
        renderHeaders: true,
        renderFooters: true,
        renderFootnotes: true,
        renderEndnotes: true,
        renderComments: false,
        debug: false,
      });

      // docx-preview không trả về HTML content, nó render trực tiếp
      setHtmlContent("rendered"); // Chỉ để đánh dấu đã render thành công
    } catch (err) {
      console.error("Error loading Word document:", err);
      setError(err instanceof Error ? err.message : "Không thể tải file Word");
    } finally {
      setLoading(false);
    }
  }, [fileUrl, viewerMode]);

  // Load document when modal opens
  useEffect(() => {
    if (open && fileUrl) {
      loadWordDocument();
    }
  }, [open, fileUrl, loadWordDocument]);

  // Reset state when modal closes
  useEffect(() => {
    if (!open) {
      setHtmlContent("");
      setError("");
      setLoading(false);
    }
  }, [open]);

  // Default container style
  const defaultContainerStyle: React.CSSProperties = useMemo(
    () => ({
      height: "100%",
      width: "100%",
      border: "1px solid #d9d9d9",
      borderRadius: "6px",
      overflow: "hidden",
      ...containerStyle,
    }),
    [containerStyle],
  );

  // Office Viewer Component
  const OfficeViewer = useCallback(
    () => (
      <div style={defaultContainerStyle} className="office-viewer">
        <iframe
          src={officeViewerUrl}
          style={{
            width: "100%",
            height: "100%",
            border: "none",
          }}
          title={`Office Viewer - ${fileName}`}
        />
      </div>
    ),
    [defaultContainerStyle, officeViewerUrl, fileName],
  );

  // DocxPreview Viewer Component
  const DocxPreviewViewer = useCallback(() => {
    if (loading) {
      return (
        <div style={{...defaultContainerStyle, display: "flex", alignItems: "center", justifyContent: "center"}}>
          <Spin size="large" tip="Đang tải file Word..." />
        </div>
      );
    }

    if (error) {
      return (
        <div style={defaultContainerStyle}>
          <Alert
            message="Lỗi tải file"
            description={error}
            type="error"
            showIcon
            action={
              <Button size="small" icon={<ReloadOutlined />} onClick={loadWordDocument}>
                Thử lại
              </Button>
            }
          />
        </div>
      );
    }

    return (
      <div style={defaultContainerStyle} className="docx-preview-viewer">
        <div
          ref={containerRef}
          style={{
            padding: "20px",
            height: "100%",
            overflow: "auto",
            backgroundColor: "#fff",
          }}
        />
      </div>
    );
  }, [loading, error, defaultContainerStyle, loadWordDocument]);

  // Tab items
  const tabItems = useMemo(() => {
    const items = [];

    if (viewerMode === "office" || viewerMode === "both") {
      items.push({
        key: "office",
        label: (
          <span>
            <FileWordOutlined />
            Office Online
          </span>
        ),
        children: <OfficeViewer />,
      });
    }

    if (viewerMode === "docx-preview" || viewerMode === "both") {
      items.push({
        key: "docx-preview",
        label: (
          <span>
            <FileWordOutlined />
            HTML Preview
          </span>
        ),
        children: <DocxPreviewViewer />,
      });
    }

    return items;
  }, [viewerMode, OfficeViewer, DocxPreviewViewer]);

  // Single viewer mode
  if (viewerMode === "office") {
    return (
      <Modal
        title={`Xem Word: ${fileName}`}
        open={open}
        onCancel={onClose}
        width={width}
        style={{top: 10}}
        styles={{
          body: {
            height: height,
            padding: 0,
          },
        }}
        footer={null}
        destroyOnClose={destroyOnClose}
        maskClosable={true}
        keyboard={true}>
        <OfficeViewer />
      </Modal>
    );
  }

  if (viewerMode === "docx-preview") {
    return (
      <Modal
        title={`Xem Word: ${fileName}`}
        open={open}
        onCancel={onClose}
        width={width}
        style={{top: 10}}
        styles={{
          body: {
            height: height,
            padding: 0,
          },
        }}
        footer={null}
        destroyOnClose={destroyOnClose}
        maskClosable={true}
        keyboard={true}>
        <DocxPreviewViewer />
      </Modal>
    );
  }

  // Both modes with tabs
  return (
    <Modal
      className="modal-word-viewer"
      title={
        <Space>
          <span>Xem Word: {fileName}</span>
          <Button type="link" size="small" icon={<DownloadOutlined />} href={fileUrl} target="_blank">
            Tải về
          </Button>
        </Space>
      }
      open={open}
      onCancel={onClose}
      width={width}
      style={{top: 10, overflow: "hidden"}}
      styles={{
        body: {
          height: height,
          padding: 0,
          overflow: "hidden",
        },
      }}
      footer={null}
      destroyOnClose={destroyOnClose}
      maskClosable={true}
      keyboard={true}>
      <Tabs activeKey={activeTab} onChange={setActiveTab} items={tabItems} style={{height: "100%"}} tabBarStyle={{margin: 0, paddingLeft: 16, paddingRight: 16}} />
    </Modal>
  );
};

WordViewerComponent.displayName = "WordViewer";
export const WordViewer = memo(WordViewerComponent, isEqual);

// Export types
export type {IWordViewerProps} from "./types";

// Export hook
export {useWordViewer} from "./useWordViewer";

// Export presets
export {WordViewerSmall, WordViewerMedium, WordViewerFullscreen, WordViewerOfficeOnly, WordViewerMammothOnly, WordViewerDocxPreviewOnly, WordViewerMobile} from "./WordViewerPresets";

export default WordViewer;
