import React, { useState } from "react";
import { Button, Space, Input, Card, Typography, Alert } from "antd";
import { FileWordOutlined } from "@ant-design/icons";
import { WordViewer } from "./index";

const { Title, Text } = Typography;

/**
 * Component test cho WordViewer với docx-preview
 */
const WordViewerTest: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [fileUrl, setFileUrl] = useState("");
  const [fileName, setFileName] = useState("Test Document");
  const [viewerMode, setViewerMode] = useState<"office" | "docx-preview" | "both">("both");

  // Sample Word document URLs for testing
  const sampleUrls = [
    {
      name: "Sample Document 1",
      url: "https://file-examples.com/storage/fe68c8a7c4c7c2c2c2c2c2c/2017/10/file_example_DOC_10kB.doc"
    },
    {
      name: "Sample Document 2", 
      url: "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf"
    }
  ];

  const handleOpenViewer = () => {
    if (!fileUrl.trim()) {
      alert("Vui lòng nhập URL file Word");
      return;
    }
    setIsOpen(true);
  };

  const handleCloseViewer = () => {
    setIsOpen(false);
  };

  const handleUseSampleUrl = (url: string, name: string) => {
    setFileUrl(url);
    setFileName(name);
  };

  return (
    <div style={{ padding: "20px", maxWidth: "800px", margin: "0 auto" }}>
      <Card>
        <Title level={2}>
          <FileWordOutlined /> WordViewer Test với docx-preview
        </Title>
        
        <Alert
          message="Hướng dẫn test"
          description="Component này đã được cập nhật để sử dụng docx-preview thay vì mammoth.js. Hãy test cả hai chế độ để đảm bảo hoạt động tốt."
          type="info"
          showIcon
          style={{ marginBottom: "20px" }}
        />

        <Space direction="vertical" size="large" style={{ width: "100%" }}>
          {/* URL Input */}
          <div>
            <Text strong>URL File Word:</Text>
            <Input
              value={fileUrl}
              onChange={(e) => setFileUrl(e.target.value)}
              placeholder="Nhập URL file Word (.docx, .doc)"
              style={{ marginTop: "8px" }}
            />
          </div>

          {/* File Name Input */}
          <div>
            <Text strong>Tên File:</Text>
            <Input
              value={fileName}
              onChange={(e) => setFileName(e.target.value)}
              placeholder="Nhập tên file"
              style={{ marginTop: "8px" }}
            />
          </div>

          {/* Viewer Mode Selection */}
          <div>
            <Text strong>Chế độ hiển thị:</Text>
            <div style={{ marginTop: "8px" }}>
              <Space>
                <Button
                  type={viewerMode === "office" ? "primary" : "default"}
                  onClick={() => setViewerMode("office")}
                >
                  Office Online
                </Button>
                <Button
                  type={viewerMode === "docx-preview" ? "primary" : "default"}
                  onClick={() => setViewerMode("docx-preview")}
                >
                  DocxPreview
                </Button>
                <Button
                  type={viewerMode === "both" ? "primary" : "default"}
                  onClick={() => setViewerMode("both")}
                >
                  Cả hai
                </Button>
              </Space>
            </div>
          </div>

          {/* Sample URLs */}
          <div>
            <Text strong>Hoặc sử dụng URL mẫu:</Text>
            <div style={{ marginTop: "8px" }}>
              <Space direction="vertical">
                {sampleUrls.map((sample, index) => (
                  <Button
                    key={index}
                    type="link"
                    onClick={() => handleUseSampleUrl(sample.url, sample.name)}
                  >
                    {sample.name}
                  </Button>
                ))}
              </Space>
            </div>
          </div>

          {/* Open Button */}
          <Button
            type="primary"
            size="large"
            icon={<FileWordOutlined />}
            onClick={handleOpenViewer}
            disabled={!fileUrl.trim()}
          >
            Mở Word Viewer
          </Button>

          {/* Current Settings Display */}
          <Card size="small" style={{ backgroundColor: "#f5f5f5" }}>
            <Text strong>Cài đặt hiện tại:</Text>
            <div style={{ marginTop: "8px" }}>
              <div>URL: <Text code>{fileUrl || "Chưa nhập"}</Text></div>
              <div>Tên file: <Text code>{fileName}</Text></div>
              <div>Chế độ: <Text code>{viewerMode}</Text></div>
            </div>
          </Card>
        </Space>
      </Card>

      {/* WordViewer Component */}
      <WordViewer
        fileUrl={fileUrl}
        fileName={fileName}
        open={isOpen}
        onClose={handleCloseViewer}
        viewerMode={viewerMode}
        width="95vw"
        height="85vh"
      />
    </div>
  );
};

export default WordViewerTest;
