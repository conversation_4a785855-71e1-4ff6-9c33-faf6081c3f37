import React, { useState } from "react";
import { Button, Space, Input, Card, Typography, Alert } from "antd";
import { FileWordOutlined, BugOutlined } from "@ant-design/icons";
import { WordViewer } from "./index";

const { Title, Text } = Typography;

const WordViewerTest: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [fileUrl, setFileUrl] = useState("https://file-examples.com/storage/fe68c8a7c4c7c2c2c2c2c2c/2017/10/file_example_DOCX_10kB.docx");
  const [fileName, setFileName] = useState("Test Document");
  const [viewerMode, setViewerMode] = useState<"office" | "docx-preview" | "both">("docx-preview");

  const handleOpenViewer = () => {
    console.log("🚀 Test: Opening WordViewer...");
    console.log("📋 Test params:", { fileUrl, fileName, viewerMode });
    setIsOpen(true);
  };

  const handleCloseViewer = () => {
    console.log("🔒 Test: Closing WordViewer...");
    setIsOpen(false);
  };

  return (
    <div style={{ padding: "20px", maxWidth: "800px", margin: "0 auto" }}>
      <Card>
        <Title level={2}>
          <BugOutlined /> WordViewer Test - Enhanced Container Mount
        </Title>
        
        <Alert
          message="Enhanced Test Component"
          description="Component này test cơ chế waitForContainer đã được cải thiện với MutationObserver và multiple strategies."
          type="info"
          showIcon
          style={{ marginBottom: "20px" }}
        />

        <Space direction="vertical" size="large" style={{ width: "100%" }}>
          <div>
            <Text strong>URL File Word:</Text>
            <Input
              value={fileUrl}
              onChange={(e) => setFileUrl(e.target.value)}
              placeholder="Nhập URL file Word (.docx)"
              style={{ marginTop: "8px" }}
            />
          </div>

          <div>
            <Text strong>Tên File:</Text>
            <Input
              value={fileName}
              onChange={(e) => setFileName(e.target.value)}
              placeholder="Nhập tên file"
              style={{ marginTop: "8px" }}
            />
          </div>

          <div>
            <Text strong>Chế độ hiển thị:</Text>
            <div style={{ marginTop: "8px" }}>
              <Space>
                <Button
                  type={viewerMode === "office" ? "primary" : "default"}
                  onClick={() => setViewerMode("office")}
                >
                  Office Online
                </Button>
                <Button
                  type={viewerMode === "docx-preview" ? "primary" : "default"}
                  onClick={() => setViewerMode("docx-preview")}
                >
                  DocxPreview (Enhanced)
                </Button>
                <Button
                  type={viewerMode === "both" ? "primary" : "default"}
                  onClick={() => setViewerMode("both")}
                >
                  Cả hai
                </Button>
              </Space>
            </div>
          </div>

          <Button
            type="primary"
            size="large"
            icon={<FileWordOutlined />}
            onClick={handleOpenViewer}
            disabled={!fileUrl.trim()}
          >
            Test WordViewer (Check Console)
          </Button>

          <Card size="small" style={{ backgroundColor: "#f5f5f5" }}>
            <Text strong>Debug Info:</Text>
            <div style={{ marginTop: "8px" }}>
              <div>URL: <Text code>{fileUrl || "Chưa nhập"}</Text></div>
              <div>Tên file: <Text code>{fileName}</Text></div>
              <div>Chế độ: <Text code>{viewerMode}</Text></div>
              <div>Status: <Text code>{isOpen ? "OPEN" : "CLOSED"}</Text></div>
            </div>
          </Card>

          <Alert
            message="Hướng dẫn debug"
            description="Mở Developer Console (F12) để xem log chi tiết về quá trình mount container và load document."
            type="warning"
            showIcon
          />
        </Space>
      </Card>

      <WordViewer
        fileUrl={fileUrl}
        fileName={fileName}
        open={isOpen}
        onClose={handleCloseViewer}
        viewerMode={viewerMode}
        width="95vw"
        height="85vh"
      />
    </div>
  );
};

export default WordViewerTest;
