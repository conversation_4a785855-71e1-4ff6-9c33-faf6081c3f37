import React from "react";
import {WordViewer} from "./index";
import {IWordViewerProps} from "./types";

/**
 * Word Viewer với kích thước nhỏ (modal nhỏ)
 */
export const WordViewerSmall: React.FC<IWordViewerProps> = props => {
  return <WordViewer {...props} width="70vw" height="60vh" />;
};

/**
 * Word Viewer với kích thước trung bình
 */
export const WordViewerMedium: React.FC<IWordViewerProps> = props => {
  return <WordViewer {...props} width="85vw" height="75vh" />;
};

/**
 * Word Viewer full screen
 */
export const WordViewerFullscreen: React.FC<IWordViewerProps> = props => {
  return (
    <WordViewer
      {...props}
      width="98vw"
      height="90vh"
      containerStyle={{
        border: "none",
        borderRadius: 0,
      }}
    />
  );
};

/**
 * Word Viewer chỉ dùng Office Online (không có Mammoth)
 */
export const WordViewerOfficeOnly: React.FC<IWordViewerProps> = props => {
  return <WordViewer {...props} viewerMode="office" />;
};

/**
 * Word Viewer chỉ dùng DocxPreview HTML (không có Office Online)
 */
export const WordViewerMammothOnly: React.FC<IWordViewerProps> = props => {
  return <WordViewer {...props} viewerMode="docx-preview" />;
};

/**
 * Word Viewer cho mobile (responsive)
 */
export const WordViewerMobile: React.FC<IWordViewerProps> = props => {
  return (
    <WordViewer
      {...props}
      width="100vw"
      height="80vh"
      viewerMode="office" // Office Online tốt hơn cho mobile
      containerStyle={{
        border: "none",
        borderRadius: 0,
      }}
    />
  );
};
