import React, {useState} from "react";
import {Button, Space} from "antd";
import {WordViewer} from "./index";

const DebugWordViewer: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [viewerMode, setViewerMode] = useState<"office" | "mammoth" | "both">("mammoth");

  // Test với một file docx mẫu - thay thế bằng URL thực tế của bạn
  const testFileUrl = "https://file-examples.com/storage/fe68c8c7c66d7b62c909f86/2017/10/file_example_DOC_100kB.doc"; // Sample DOC file

  return (
    <div style={{padding: "20px"}}>
      <h2>Debug WordViewer - docx-preview</h2>

      <Space direction="vertical">
        <div>
          <strong>Test URL:</strong> {testFileUrl}
        </div>

        <Space>
          <Button type={viewerMode === "mammoth" ? "primary" : "default"} onClick={() => setViewerMode("mammoth")}>
            Mammoth Only
          </Button>
          <Button type={viewerMode === "both" ? "primary" : "default"} onClick={() => setViewerMode("both")}>
            Both Modes
          </Button>
          <Button type={viewerMode === "office" ? "primary" : "default"} onClick={() => setViewerMode("office")}>
            Office Only
          </Button>
        </Space>

        <Button
          type="primary"
          size="large"
          onClick={() => {
            console.log("Opening WordViewer with URL:", testFileUrl, "Mode:", viewerMode);
            setIsOpen(true);
          }}>
          Test WordViewer ({viewerMode})
        </Button>
      </Space>

      <WordViewer
        fileUrl={testFileUrl}
        fileName="Test Document.doc"
        open={isOpen}
        onClose={() => {
          console.log("Closing WordViewer");
          setIsOpen(false);
        }}
        viewerMode={viewerMode}
        width="90vw"
        height="80vh"
      />
    </div>
  );
};

export default DebugWordViewer;
