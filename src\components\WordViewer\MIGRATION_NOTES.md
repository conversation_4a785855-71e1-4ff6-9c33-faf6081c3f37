# WordViewer Migration: Mammoth.js → docx-preview

## Tổng quan thay đổi

Component WordViewer đã được cập nhật để sử dụng thư viện `docx-preview` thay vì `mammoth.js` để hiển thị tài liệu Word (.docx).

## Lý do thay đổi

- **docx-preview** cung cấp khả năng hiển thị tài liệu Word gần giống với bản gốc hơn
- Hỗ trợ nhiều tính năng Word nâng cao hơn (headers, footers, comments, page breaks)
- <PERSON><PERSON><PERSON> được định dạng phức tạp (fonts, colors, spacing, borders)
- Cộng đồng sử dụng lớn và được cập nhật thường xuyên

## Thay đổi chính

### 1. Import và Dependencies
```typescript
// Trước (mammoth.js)
const mammoth = await import("mammoth");
const result = await mammoth.convertToHtml({arrayBuffer});

// Sau (docx-preview)
const docx = await import("docx-preview");
await docx.renderAsync(arrayBuffer, containerRef.current, null, options);
```

### 2. State Management
```typescript
// Trước
const [htmlContent, setHtmlContent] = useState<string>("");

// Sau
const docxContainerRef = useRef<HTMLDivElement>(null);
// Không cần htmlContent state vì docx-preview render trực tiếp vào DOM
```

### 3. Rendering Method
```typescript
// Trước (mammoth.js)
<div dangerouslySetInnerHTML={{__html: htmlContent}} />

// Sau (docx-preview)
<div ref={docxContainerRef} />
```

### 4. Component Names
- `MammothViewer` → `DocxPreviewViewer`
- Cập nhật tất cả references và function names

## Cấu hình docx-preview

Component sử dụng các options sau cho docx-preview:

```typescript
{
  className: "docx-preview",
  inWrapper: true,
  ignoreWidth: false,
  ignoreHeight: false,
  ignoreFonts: false,
  breakPages: true,
  ignoreLastRenderedPageBreak: true,
  experimental: false,
  trimXmlDeclaration: true,
  useBase64URL: false,
  renderHeaders: true,
  renderFooters: true,
  renderFootnotes: true,
  renderEndnotes: true,
  renderComments: false,
  debug: false,
}
```

## CSS Updates

Đã thêm styles mới cho docx-preview trong `index.default.scss`:

```scss
.docx-preview-viewer {
  .docx-preview {
    font-family: 'Times New Roman', serif;
    line-height: 1.5;
    // ... additional styles
  }
}
```

## API Compatibility

Component vẫn giữ nguyên tất cả props và functionality:
- `fileUrl`: URL của file Word
- `fileName`: Tên file hiển thị
- `open`: Trạng thái mở/đóng modal
- `onClose`: Callback khi đóng modal
- `viewerMode`: 'office' | 'mammoth' | 'both' (giữ nguyên tên 'mammoth' để backward compatibility)
- Các props khác: `width`, `height`, `destroyOnClose`, `containerStyle`

## Testing

Sử dụng file `test-docx-preview.tsx` để test component:

```typescript
import TestDocxPreview from './test-docx-preview';
// Render component để test các chế độ viewer khác nhau
```

## Performance Considerations

- docx-preview có thể chậm hơn mammoth.js với documents lớn
- Sử dụng dynamic import để tối ưu bundle size
- Clear container content khi modal đóng để tránh memory leaks

## Backward Compatibility

- Tất cả props hiện tại vẫn hoạt động
- ViewerMode 'mammoth' vẫn hoạt động (sử dụng docx-preview)
- Không cần thay đổi code ở các component sử dụng WordViewer

## Dependencies

Đảm bảo package.json có:
```json
{
  "dependencies": {
    "docx-preview": "^0.3.6"
  }
}
```

Package `mammoth` có thể được remove nếu không sử dụng ở nơi khác.
